--[[
================================================================================
游戏主循环计时器
================================================================================
文件名: mainticker.lua
功能: 游戏客户端的主循环计时器，负责更新所有需要定时处理的游戏模块和UI界面
作者: 大唐游戏开发团队
创建时间: 未知
最后修改: 未知
================================================================================
]]--

-- ============================================================================
-- 依赖模块引入
-- ============================================================================
require "logic.characterinfo.getdatainfomsg"        -- 角色数据信息消息
require "logic.task.renwulistdialog"                -- 任务列表对话框
require "logic.lottery.lotterycarddlg"              -- 抽奖卡对话框
require "logic.specialeffect.specialeffectmanager"  -- 特效管理器
require "logic.lockscreendlg"                       -- 锁屏对话框
require "logic.xingongnengkaiqi.xingongnengopendlg" -- 新功能开启对话框
require "logic.shop.mallshop"                       -- 商城
require "logic.answerquestion.answerquestiondlg"    -- 答题对话框
require "logic.answerquestion.kejuxiangshi"         -- 科举详细
debugrequire "logic.leitai.leitaidatamanager"       -- 擂台数据管理器(调试模式)
require "logic.shop.stalldlg"                       -- 摆摊对话框

-- ============================================================================
-- 全局变量定义
-- ============================================================================
local gcTime = 0                -- 垃圾回收计时器
--local serverStartTime = 0     -- 服务器启动时间(已注释)
--local nativeTime = -1         -- 本地时间(已注释)

--[[
游戏主循环函数
功能: 每帧调用，更新所有需要定时处理的游戏模块和UI界面
参数: delta - 距离上次调用的时间间隔(毫秒)
]]--
function LuaMainTick(delta)

    -- ============================================================================
    -- 核心系统更新
    -- ============================================================================

    -- 新角色引导管理器更新(转换为秒)
    NewRoleGuideManager_Run(delta*0.001)

    -- 新物品特效系统更新
    AddNewItemsEffect.Run(delta)

    -- 隐藏系统更新
	if YinCang.getInstanceNotCreate() then
		YinCang.getInstanceNotCreate():Run(delta)
	end

    -- 主控制器更新
	if MainControl.getInstanceNotCreate() then
		MainControl.getInstanceNotCreate():run(delta)
	end

    -- 数据信息消息处理
	GetDataInfoMsg.run(delta)

    -- ============================================================================
    -- UI对话框系统更新
    -- ============================================================================

    -- 任务列表对话框更新
	if Renwulistdialog.getSingleton() then
		Renwulistdialog.getSingleton():Run(delta)
	end

    -- 锁屏对话框更新
	if LockScreenDlg.getInstanceNotCreate() then
		LockScreenDlg.getInstanceNotCreate():run(delta)
	end

    -- 新功能开启对话框更新
	if XinGongNengOpenDLG.getInstanceNotCreate() then
		XinGongNengOpenDLG.getInstanceNotCreate():run(delta)
	end

    -- 商城更新
    if MallShop.getInstanceNotCreate() then
        MallShop.getInstanceNotCreate():update(delta)
    end

    -- ============================================================================
    -- NPC和特效系统更新
    -- ============================================================================

    -- NPC协议处理器更新(转换为秒)
	require "handler.fire_pb_npc"(delta / 1000)

    -- 特效管理器更新
	if SpecialEffectManager.getInstanceNotCreate() then
		SpecialEffectManager.getInstanceNotCreate():run(delta)
	end

    -- NPC场景对话更新
    if require("logic.npc.npcscenespeakdialog").getInstanceNotCreate() then
		require("logic.npc.npcscenespeakdialog").getInstanceNotCreate():run(delta)
	end

    -- NPC对话框更新
    if require("logic.npc.npcdialog").getInstanceNotCreate() then
		require("logic.npc.npcdialog").getInstanceNotCreate():run(delta)
	end

    -- 队伍分配对话框更新
	if require("logic.team.teamrollmelondialog").getInstanceNotCreate() then
		require("logic.team.teamrollmelondialog").getInstanceNotCreate():run(delta)
	end

    -- ============================================================================
    -- 宠物系统对话框更新
    -- ============================================================================

    -- 捕获对话框更新
    local dlg = require("logic.capturedlg").getInstanceNotCreate()
    if dlg then
        dlg:run(delta)
    end

	local dlg = require("logic.pet.petpropertydlgnew").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

	dlg = require("logic.pet.petlianyaodlg").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

	dlg = require("logic.pet.petcombineresultdlg").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

	dlg = require("logic.pet.petgallerydlg").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

    dlg = require ("logic.shijiebobaodlg").getInstanceNotCreate()
    if dlg then
        dlg:update(delta)
    end

    require("logic.team.huobanzhuzhandialog").updateTime(delta)

	dlg = require("logic.team.huobanzhuzhandialog").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

	dlg = require("logic.team.huobanzhuzhaninfo").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

	dlg = require("logic.team.teammatchdlg").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

	dlg = require("logic.treasureMap.treasureChosedDlg").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

	--new add
	dlg = require("logic.qiandaosongli.continuedayreward").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end
    dlg = require("logic.qiandaosongli.dailypayreward").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

	dlg = require("logic.treasureMap.supertreasuremap").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

	require("manager.buffmanager").getInstance():update(delta)
	require("logic.task.taskmanager").getInstance():update(delta)
	--dlg = require("logic.treasureMap.treasureChosedDlg").getInstanceNotCreate()
	if treasureChosedDlg.startTimer == true then
		treasureChosedDlg.tick(delta)
	end

	dlg = require("logic.pet.petdetaildlg").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

    dlg = require("logic.reconnectdlg").getInstanceNotCreate()
    if dlg then
        dlg:tick(delta)
    end

    dlg = require("logic.family.familychuangjiandialog").getInstanceNotCreate()
    if dlg then
        dlg:update(delta)
    end

    dlg = require("logic.family.familyqunfaxiaoxidialog").getInstanceNotCreate()
    if dlg then
        dlg:update(delta)
    end

    dlg = require("logic.family.familyjiarudialog").getInstanceNotCreate()
    if dlg then
        dlg:update(delta)
    end

	dlg = require("logic.worldmap.worldmapdlg").GetSingleton()
    if dlg then
        dlg:update(delta)
    end

	dlg = require("logic.worldmap.worldmapdlg1").GetSingleton()
    if dlg then
        dlg:update(delta)
    end
    dlg = require ("logic.family.familyyaofang").getInstanceNotCreate()
    if dlg then
       dlg:update(delta)
    end

    dlg = require ("logic.jingling.jinglingdlg").getInstanceNotCreate()
    if dlg then
        dlg:run(delta / 1000)
    end

    dlg = require ("logic.libaoduihuan.libaoduihuanma").getInstanceNotCreate()
    if dlg then
       dlg:update(delta)
    end

    dlg = require ("logic.answerquestion.answerquestiondlg").getInstanceNotCreate()
	if dlg then
		dlg:UpdateTime(delta)
	end

    dlg = require ("logic.ranse.characterransedlg").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

    dlg = require ("logic.chat.cchatoutboxoperateldlg").getInstanceNotCreate()
	if dlg then
		dlg:UpdateChatContentBox(delta)
	end

	dlg = require ("logic.chat.chatoutputdialog").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

	dlg = GetChatManager()
	if dlg then
		dlg:update(delta)
	end

	dlg = require ("logic.answerquestion.kejuxiangshi").getInstanceNotCreate()
	if dlg then
		dlg:UpdateTime(delta)
	end

    dlg = require ("logic.friend.sendgiftdialog").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

    dlg = require("logic.fuyuanbox.fuyuanboxdlg").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

    dlg = require("logic.wisdomtrialdlg.wisdomtrialdlg").getInstanceNotCreate()
	if dlg then
		dlg:UpdateTime(delta)
	end

    dlg = require("logic.busytext.busytextdlg").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

    dlg = require("logic.logo.logoinfodlg").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

    dlg = require("logic.fuben.fubenEnterDlg").getInstanceNotCreate()
    if dlg then
        dlg:run(delta)
    end
    dlg = require("logic.title.titledlg").getInstanceNotCreate()
    if dlg then
        dlg:Update()
    end

    dlg = require("logic.schoolLeader.leaderElectionDlg").getInstanceNotCreate()
    if dlg then
        dlg:run(delta)
    end
    dlg = require("logic.redpack.redpackdlg").getInstanceNotCreate()
    if dlg then
        dlg:Update(delta)
    end
    dlg = require("logic.redpack.redpacksenddlg").getInstanceNotCreate()
    if dlg then
        dlg:Update(delta)
    end
    dlg = require("logic.redpack.redpackhistorydlg").getInstanceNotCreate()
    if dlg then
        dlg:Update(delta)
    end
    if CurrencyManager.scheduleForAutoBuyRes then
        CurrencyManager.tick(delta)
    end
    LeiTaiDataManager.Run(delta)

	LuaBattleUIManager.Tick(delta)

    dlg = require ("logic.guajicfg").getInstanceNotCreate()
	if dlg then
		dlg:update(delta)
	end

    dlg = require ("logic.newswarndlg").getInstanceNotCreate()
    if dlg then
		dlg:update(delta)
	end

     local datamanager = require "logic.faction.factiondatamanager"
     if datamanager then
      datamanager.OnTick(delta)
     end

    dlg = require("logic.gaimingka").getInstanceNotCreate()
    if dlg then
        dlg:update(delta)
    end

    dlg = require("logic.task.taskdialog").getInstanceNotCreate()
    if dlg then
        dlg:update(delta)
    end

    require("logic.task.schedulermanager").getInstance():update(delta)

    Tick1Minute(delta)
    TickFPSCheck(delta)
    --TickFPS(delta)

    dlg = StallDlg.getInstanceNotCreate()
    if dlg then
        dlg:update(delta)
    end

    local roleItemManager = require("logic.item.roleitemmanager").getInstance()
    roleItemManager:update(delta)

    dlg = require("logic.tips.commontipdlg").getInstanceNotCreate()
    if dlg then
        dlg:Update(delta)
    end

    dlg = familybossBloodBar.getInstanceNotCreate()
    if dlg then
        dlg:run(delta)
    end

    dlg = require"logic.huodong.huodongmanager".getInstanceNotCreate()
    if dlg then
        dlg:Update(delta)
    end

    if GetTeamManager() then
        GetTeamManager():Run(delta)
    end

    if gGetWelfareManager() then
        gGetWelfareManager():run(delta)
    end

	checkShortcutItemLaunchedBy();

    dlg = require"logic.pointcardserver.currencytradingdlg".getInstanceNotCreate()
    if dlg then
        dlg:update(delta)
    end

    dlg = require "logic.monthcard.monthcarddlg".getInstanceNotCreate()
	if dlg then
		dlg:update()
	end

    dlg = require ("logic.qqgift.qqgiftdlg").getInstanceNotCreate()
    if dlg then
       dlg:update(delta)
    end

    local familyfightmgr = require("logic.family.familyfightmanager").getInstance()
    if familyfightmgr then
        familyfightmgr:update(delta)
    end

    dlg = require"logic.family.familyfightxianshi".getInstanceNotCreate()
    if dlg then
        dlg:update(delta)
    end

    dlg = require"logic.family.familyduizhanzudui".getInstanceNotCreate()
    if dlg then
       dlg:update(delta)
    end

    dlg = require("logic.family.familyxiangyingtanhedialog").getInstanceNotCreate()
    if dlg then
        dlg:update(delta)
    end

    dlg = require("logic.recruit.recruitdlg").getInstanceNotCreate()
    if dlg then
        dlg:Update()
    end

    dlg = require"logic.gamewaitingdlg".getInstanceNotCreate()
    if dlg then
        dlg:Update(delta)
    end

    if gGetDataManager() then
        gGetDataManager():update(delta)
    end

    dlg = require("logic.shoujianquan.shoujiguanlianshuru").getInstanceNotCreate()
    if dlg then
        dlg:CountDownUpdate(delta)
    end

    dlg = require("logic.shoujianquan.shoujiguanlianchenggong").getInstanceNotCreate()
    if dlg then
        dlg:CountDownUpdate(delta)
    end

    require("logic.chargecell").update(delta)

end

-- ============================================================================
-- 定时器相关函数
-- ============================================================================

-- 1分钟计时器
local tick1min = 0

--[[
每分钟执行一次的定时器
功能: 处理需要每分钟执行的逻辑
参数: delta - 时间间隔(毫秒)
]]--
function Tick1Minute(delta)
    tick1min = tick1min + delta
    if tick1min >= 60000 then  -- 60秒 = 60000毫秒
       tick1min = tick1min - 60000
       -- 检查玩家等级>=60且不在副本中且不在战斗中
        if gGetDataManager() and gGetDataManager():GetMainCharacterLevel() >= 60 and not gGetScene():IsInFuben() and not GetBattleManager():IsInBattle() then
           -- 预留的逻辑处理位置
        end
    end
end

-- FPS检查计时器
local tick3min = 0

--[[
FPS检查和客户端时间同步
功能: 每分钟向服务器发送客户端时间进行同步
参数: delta - 时间间隔(毫秒)
]]--
function TickFPSCheck(delta)
    tick3min = tick3min + delta
    if tick3min >= 60000 then  -- 60秒检查一次
       tick3min = tick3min - 60000
       -- 创建客户端时间协议并发送给服务器
        local p = require "protodef.fire.pb.game.cclienttime":new()
        p.time = gGetServerTime()
        require "manager.luaprotocolmanager":send(p)
    end
end

-- FPS控制相关变量
local tickFPSctrl = 0    -- FPS控制计时器
local lastFPS = 0        -- 上次记录的FPS值
PlayerCountByFPS = 150   -- 根据FPS调整的玩家显示数量

--[[
FPS监控和场景优化
功能: 根据FPS动态调整场景效果和玩家显示数量
参数: delta - 时间间隔(毫秒)
]]--
function TickFPS(delta)
    tickFPSctrl = tickFPSctrl + delta
    if tickFPSctrl >= 3000 then  -- 每3秒检查一次
        tickFPSctrl = tickFPSctrl - 3000
        local fps = Nuclear.GetEngine():GetFPS()

        -- 根据FPS动态控制场景特效
        if not gGetScene():isLoadMaping() and gGetGameConfigManager():GetConfigValue("sceneeffect") == 1 then
            if  fps < 15 then
                gGetScene():pauseSceneEffects()  -- FPS过低时暂停场景特效
            else
                gGetScene():resumeSceneEffects() -- FPS正常时恢复场景特效
            end
        end

        -- 检查FPS变化是否显著(>=2)
        local det = math.abs(fps - lastFPS)
        if det >= 2 then
            lastFPS = fps

            -- 根据FPS调整玩家显示数量
            local tableAllId = BeanConfigManager.getInstance():GetTableByName("SysConfig.cpcountfpssetting"):getAllID()
            for _, v in pairs(tableAllId) do
                local fpsinfo = BeanConfigManager.getInstance():GetTableByName("SysConfig.cpcountfpssetting"):getRecorder(v)
                if fpsinfo.minfps < lastFPS and lastFPS <= fpsinfo.maxfps then
                    if PlayerCountByFPS ~= fpsinfo.playercount then
                        PlayerCountByFPS = fpsinfo.playercount
                        SystemSettingNewDlg.SendMaxPlayerNum()  -- 发送新的玩家数量设置
                    end
                    break
                end
            end
        end
    end
end

--[[
重置服务器计时器
功能: 重置服务器相关的计时器状态
]]--
function ResetServerTimer()
    -- nativeTime = -1  -- 原代码已注释
end

-- ============================================================================
-- 快捷方式处理函数
-- ============================================================================

--[[
检查快捷方式启动项处理
功能: 处理iOS 9快捷菜单启动的功能项，如查看摆摊、好友聊天、活动日历等
]]--
function checkShortcutItemLaunchedBy()
	-- ios9 ��ݲ˵�����
	local shortcutItem = gGetLoginManager():getShortcutItemLaunchedBy();
	local handled = gGetLoginManager():isShortcutItemHandled();
	if shortcutItem ~= eShortcutItem_None and not handled then

		-- ��ȡ��ҵȼ�
		local data = gGetDataManager():GetMainCharacterData();
		local nLvl = data:GetValue(fire.pb.attr.AttrType.LEVEL);

		if shortcutItem == eShortcutItem_ViewStall then
			require("logic.shop.stalllabel").show()

		elseif shortcutItem == eShortcutItem_FriendChat then
			require "logic.friend.friendmaillabel".Show(1)

		elseif shortcutItem == eShortcutItem_ActivityCalendar then

			-- ��п����ȼ���Ҫ��
			local needLevel = BeanConfigManager.getInstance():GetTableByName("mission.cnewfunctionopen"):getRecorder(14).needlevel;
			if nLvl >= needLevel then
				require "logic.huodong.huodongmanager".getInstance();
				require "logic.logo.logoinfodlg".openHuoDongDlg();
			end

		end

		gGetLoginManager():setShortcutItemHandled(true);
	end
end